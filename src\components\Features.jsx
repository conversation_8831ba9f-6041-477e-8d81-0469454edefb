import { motion } from 'framer-motion'
import { 
  Eye, 
  Mic, 
  Navigation, 
  QrCode, 
  Video, 
  Brain,
  Shield,
  Zap,
  Users
} from 'lucide-react'

const Features = () => {
  const mainFeatures = [
    {
      icon: Eye,
      title: 'Détection d\'Environnement',
      description: 'Analyse en temps réel de l\'environnement avec détection d\'obstacles et de mouvement',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: QrCode,
      title: 'Lecture QR Codes',
      description: 'Identification et navigation basées sur la lecture de QR codes pour localisation précise',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Mic,
      title: 'Interaction Vocale IA',
      description: 'Dialogue intelligent avec les utilisateurs via Gemini API pour une communication naturelle',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Video,
      title: 'Streaming Temps Réel',
      description: 'Affichage en direct du flux caméra avec interface de contrôle intuitive',
      color: 'from-red-500 to-orange-500'
    },
    {
      icon: Navigation,
      title: 'Déplacement Autonome',
      description: 'Navigation intelligente dans l\'espace avec évitement d\'obstacles automatique',
      color: 'from-indigo-500 to-blue-500'
    },
    {
      icon: Brain,
      title: 'Reconnaissance d\'Objets',
      description: 'Identification et classification d\'objets pour améliorer l\'interaction environnementale',
      color: 'from-yellow-500 to-amber-500'
    }
  ]

  const functionalNeeds = [
    {
      icon: Users,
      title: 'Interaction Utilisateur',
      description: 'L\'utilisateur peut interagir vocalement avec le robot de manière naturelle'
    },
    {
      icon: Shield,
      title: 'Déplacement Sécurisé',
      description: 'Le robot doit pouvoir se déplacer de manière autonome en évitant les obstacles'
    },
    {
      icon: Zap,
      title: 'Traitement Temps Réel',
      description: 'Affichage du flux vidéo et détection des objets ou QR codes en temps réel'
    }
  ]

  const improvements = [
    'Intégration de la reconnaissance faciale pour une interaction plus personnalisée',
    'Amélioration de la stabilité du robot sur différents terrains',
    'Extension des fonctionnalités vocales avec une IA multilingue',
    'Optimisation de la consommation énergétique',
    'Interface mobile pour contrôle à distance'
  ]

  return (
    <section id="features" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Fonctionnalités Principales</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Notre robot assistant intègre des technologies avancées pour offrir une expérience 
            d'interaction complète et intelligente.
          </p>
        </motion.div>

        {/* Main Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {mainFeatures.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <div className={`w-16 h-16 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4`}>
                <feature.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Functional Needs */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-gray-800 mb-8 text-center">Besoins Fonctionnels</h3>
          <div className="grid md:grid-cols-3 gap-8">
            {functionalNeeds.map((need, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl p-6 border-l-4 border-blue-500 shadow-md"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                viewport={{ once: true }}
              >
                <need.icon className="w-12 h-12 text-blue-500 mb-4" />
                <h4 className="text-lg font-semibold text-gray-800 mb-3">{need.title}</h4>
                <p className="text-gray-600">{need.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Future Improvements */}
        <motion.div
          className="bg-white rounded-2xl p-8 shadow-lg"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-gray-800 mb-8 text-center">Perspectives d'Amélioration</h3>
          <div className="grid md:grid-cols-2 gap-6">
            {improvements.map((improvement, index) => (
              <motion.div
                key={index}
                className="flex items-start space-x-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-gray-700">{improvement}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Features
