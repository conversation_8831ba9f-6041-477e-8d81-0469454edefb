import { motion } from 'framer-motion'
import { Target, Users, Award, Lightbulb } from 'lucide-react'

const About = () => {
  const stats = [
    { icon: Target, label: 'Objectif Principal', value: 'Interaction Autonome' },
    { icon: Users, label: 'Équipe', value: 'Étudiants ISI' },
    { icon: Award, label: 'Partenariat', value: 'Université Tunis El Manar' },
    { icon: Lightbulb, label: 'Innovation', value: 'IA + Robotique' },
  ]

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">À Propos du Projet</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Notre projet consiste en la conception et la réalisation d'un robot assistant humanoïde intelligent, 
            réalisé dans le cadre de notre Projet de Fin d'Études.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl font-bold text-gray-800 mb-6">Objectif Principal</h3>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed">
              L'objectif principal du projet est de développer un robot capable d'interagir avec son 
              environnement et les humains de manière autonome, grâce à l'intégration de plusieurs 
              technologies matérielles et logicielles modernes.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <p className="text-gray-600">Détection et analyse de l'environnement en temps réel</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <p className="text-gray-600">Interaction vocale intelligente avec les utilisateurs</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <p className="text-gray-600">Navigation autonome et reconnaissance d'objets</p>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <p className="text-gray-600">Streaming vidéo et lecture de QR codes</p>
              </div>
            </div>
          </motion.div>

          {/* Right Content - Stats */}
          <motion.div
            className="grid grid-cols-2 gap-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl text-center"
                whileHover={{ scale: 1.05, y: -5 }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <stat.icon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h4 className="font-semibold text-gray-800 mb-2">{stat.label}</h4>
                <p className="text-sm text-gray-600">{stat.value}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Technologies Section */}
        <motion.div
          className="bg-gray-50 rounded-2xl p-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-gray-800 mb-8 text-center">Technologies Utilisées</h3>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Hardware */}
            <div>
              <h4 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <span className="w-3 h-3 bg-blue-600 rounded-full mr-3"></span>
                Environnement Matériel
              </h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Raspberry Pi avec caméra intégrée</li>
                <li>• Caméra Pi pour streaming vidéo en direct</li>
                <li>• Capteurs de détection d'obstacles</li>
                <li>• Moteurs et châssis pour déplacement</li>
              </ul>
            </div>

            {/* Software */}
            <div>
              <h4 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <span className="w-3 h-3 bg-green-600 rounded-full mr-3"></span>
                Environnement Logiciel
              </h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Gemini API pour interaction vocale IA</li>
                <li>• OpenCV pour reconnaissance d'objets</li>
                <li>• Python comme langage principal</li>
                <li>• Flask pour interface de contrôle</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
