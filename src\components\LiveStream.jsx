import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Settings,
  Wifi,
  WifiOff,
  Camera,
  Mic,
  MicOff
} from 'lucide-react'

const LiveStream = () => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [isMicEnabled, setIsMicEnabled] = useState(false)
  const [streamQuality, setStreamQuality] = useState('720p')
  const videoRef = useRef(null)

  // Simulate connection status
  useEffect(() => {
    const interval = setInterval(() => {
      setIsConnected(prev => Math.random() > 0.1) // 90% chance of being connected
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying)
    // Here you would implement actual video play/pause logic
  }

  const handleMute = () => {
    setIsMuted(!isMuted)
    // Here you would implement actual audio mute logic
  }

  const handleMicToggle = () => {
    setIsMicEnabled(!isMicEnabled)
    // Here you would implement actual microphone toggle logic
  }

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen()
      }
    }
  }

  const qualityOptions = ['480p', '720p', '1080p']

  return (
    <section id="livestream" className="py-20 bg-gradient-to-br from-gray-900 to-blue-900 text-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">Streaming en Temps Réel</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Observez le robot en action grâce au flux vidéo en direct de sa caméra embarquée. 
            Interagissez avec lui via les commandes vocales.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Video Stream */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-black rounded-xl overflow-hidden shadow-2xl">
              {/* Video Container */}
              <div 
                ref={videoRef}
                className="relative aspect-video bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center"
              >
                {/* Placeholder for actual video stream */}
                <div className="text-center">
                  <Camera className="w-20 h-20 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">
                    {isConnected ? 'Flux vidéo en direct' : 'Connexion en cours...'}
                  </p>
                  {isConnected && (
                    <div className="mt-4">
                      <div className="w-4 h-4 bg-red-500 rounded-full mx-auto animate-pulse"></div>
                      <p className="text-red-400 text-sm mt-2">● LIVE</p>
                    </div>
                  )}
                </div>

                {/* Connection Status Indicator */}
                <div className="absolute top-4 right-4">
                  {isConnected ? (
                    <div className="flex items-center space-x-2 bg-green-600/80 px-3 py-1 rounded-full">
                      <Wifi className="w-4 h-4" />
                      <span className="text-sm">Connecté</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 bg-red-600/80 px-3 py-1 rounded-full">
                      <WifiOff className="w-4 h-4" />
                      <span className="text-sm">Déconnecté</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Video Controls */}
              <div className="bg-gray-800 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <motion.button
                      onClick={handlePlayPause}
                      className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                    </motion.button>

                    <motion.button
                      onClick={handleMute}
                      className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                    </motion.button>

                    <div className="text-sm text-gray-300">
                      Qualité: {streamQuality}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <motion.button
                      onClick={handleFullscreen}
                      className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Maximize className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Control Panel */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Voice Interaction */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Mic className="w-6 h-6 mr-2" />
                Interaction Vocale
              </h3>
              
              <motion.button
                onClick={handleMicToggle}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
                  isMicEnabled 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-green-600 hover:bg-green-700'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isMicEnabled ? (
                  <div className="flex items-center justify-center space-x-2">
                    <MicOff className="w-5 h-5" />
                    <span>Arrêter l'écoute</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Mic className="w-5 h-5" />
                    <span>Commencer à parler</span>
                  </div>
                )}
              </motion.button>

              {isMicEnabled && (
                <motion.div
                  className="mt-4 p-3 bg-green-600/20 rounded-lg"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                >
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm">En écoute...</span>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Stream Settings */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <Settings className="w-6 h-6 mr-2" />
                Paramètres Stream
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Qualité Vidéo</label>
                  <select
                    value={streamQuality}
                    onChange={(e) => setStreamQuality(e.target.value)}
                    className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  >
                    {qualityOptions.map(quality => (
                      <option key={quality} value={quality}>{quality}</option>
                    ))}
                  </select>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-reconnexion</span>
                  <div className="w-12 h-6 bg-green-600 rounded-full relative">
                    <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Robot Status */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <h3 className="text-xl font-semibold mb-4">État du Robot</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Batterie</span>
                  <span className="text-sm text-green-400">87%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Température CPU</span>
                  <span className="text-sm text-yellow-400">45°C</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Obstacles détectés</span>
                  <span className="text-sm text-blue-400">2</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Mode</span>
                  <span className="text-sm text-green-400">Autonome</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default LiveStream
