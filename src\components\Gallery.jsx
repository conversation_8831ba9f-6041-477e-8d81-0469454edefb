import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Play, Image as ImageIcon, Video } from 'lucide-react'

const Gallery = () => {
  const [selectedMedia, setSelectedMedia] = useState(null)
  const [filter, setFilter] = useState('all')

  // Placeholder media items - you can replace these with actual project media
  const mediaItems = [
    {
      id: 1,
      type: 'image',
      src: '/api/placeholder/400/300',
      title: 'Robot Assembly',
      description: 'Assemblage du châssis et des composants principaux',
      category: 'hardware'
    },
    {
      id: 2,
      type: 'video',
      src: '/api/placeholder/400/300',
      title: 'Navigation Demo',
      description: 'Démonstration de navigation autonome',
      category: 'demo'
    },
    {
      id: 3,
      type: 'image',
      src: '/api/placeholder/400/300',
      title: 'Raspberry Pi Setup',
      description: 'Configuration du Raspberry Pi et des capteurs',
      category: 'hardware'
    },
    {
      id: 4,
      type: 'image',
      src: '/api/placeholder/400/300',
      title: 'Interface de Contrôle',
      description: 'Interface Flask pour le contrôle du robot',
      category: 'software'
    },
    {
      id: 5,
      type: 'video',
      src: '/api/placeholder/400/300',
      title: 'Interaction Vocale',
      description: 'Test de l\'interaction vocale avec Gemini API',
      category: 'demo'
    },
    {
      id: 6,
      type: 'image',
      src: '/api/placeholder/400/300',
      title: 'Détection QR Code',
      description: 'Système de lecture et reconnaissance QR codes',
      category: 'software'
    },
    {
      id: 7,
      type: 'video',
      src: '/api/placeholder/400/300',
      title: 'Reconnaissance d\'Objets',
      description: 'OpenCV en action pour la reconnaissance d\'objets',
      category: 'demo'
    },
    {
      id: 8,
      type: 'image',
      src: '/api/placeholder/400/300',
      title: 'Équipe de Développement',
      description: 'L\'équipe d\'étudiants ISI travaillant sur le projet',
      category: 'team'
    }
  ]

  const categories = [
    { id: 'all', label: 'Tout', icon: ImageIcon },
    { id: 'hardware', label: 'Matériel', icon: ImageIcon },
    { id: 'software', label: 'Logiciel', icon: ImageIcon },
    { id: 'demo', label: 'Démonstrations', icon: Video },
    { id: 'team', label: 'Équipe', icon: ImageIcon }
  ]

  const filteredItems = filter === 'all' 
    ? mediaItems 
    : mediaItems.filter(item => item.category === filter)

  return (
    <section id="gallery" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">Galerie du Projet</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Découvrez les étapes de développement, les démonstrations et l'équipe 
            derrière ce projet innovant de robotique.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              onClick={() => setFilter(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-full font-medium transition-all ${
                filter === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <category.icon className="w-4 h-4" />
              <span>{category.label}</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Media Grid */}
        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          layout
        >
          <AnimatePresence>
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.4, delay: index * 0.05 }}
                className="group cursor-pointer"
                onClick={() => setSelectedMedia(item)}
              >
                <div className="relative overflow-hidden rounded-xl bg-gray-200 aspect-[4/3]">
                  {/* Placeholder for actual image/video */}
                  <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                    {item.type === 'video' ? (
                      <div className="text-center">
                        <Play className="w-12 h-12 text-blue-600 mx-auto mb-2" />
                        <span className="text-sm text-gray-600">Vidéo</span>
                      </div>
                    ) : (
                      <div className="text-center">
                        <ImageIcon className="w-12 h-12 text-blue-600 mx-auto mb-2" />
                        <span className="text-sm text-gray-600">Image</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-all duration-300 flex items-center justify-center">
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      initial={{ scale: 0.8 }}
                      whileHover={{ scale: 1 }}
                    >
                      {item.type === 'video' ? (
                        <Play className="w-12 h-12 text-white" />
                      ) : (
                        <ImageIcon className="w-12 h-12 text-white" />
                      )}
                    </motion.div>
                  </div>
                </div>
                
                <div className="mt-4">
                  <h3 className="font-semibold text-gray-800 mb-1">{item.title}</h3>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Modal */}
        <AnimatePresence>
          {selectedMedia && (
            <motion.div
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setSelectedMedia(null)}
            >
              <motion.div
                className="relative max-w-4xl w-full bg-white rounded-xl overflow-hidden"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <button
                  className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors"
                  onClick={() => setSelectedMedia(null)}
                >
                  <X className="w-6 h-6" />
                </button>
                
                <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                  {selectedMedia.type === 'video' ? (
                    <div className="text-center">
                      <Play className="w-20 h-20 text-blue-600 mx-auto mb-4" />
                      <p className="text-gray-600">Lecteur vidéo - {selectedMedia.title}</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <ImageIcon className="w-20 h-20 text-blue-600 mx-auto mb-4" />
                      <p className="text-gray-600">Image - {selectedMedia.title}</p>
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">{selectedMedia.title}</h3>
                  <p className="text-gray-600">{selectedMedia.description}</p>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  )
}

export default Gallery
